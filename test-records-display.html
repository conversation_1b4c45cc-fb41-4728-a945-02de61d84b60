<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试记录显示验证</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .records-table {
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
        }
        .table-header {
            background: #f8f9fa;
            display: grid;
            grid-template-columns: 2fr 2fr 1fr 1fr;
            gap: 1px;
            padding: 12px;
            font-weight: 600;
            border-bottom: 1px solid #e0e0e0;
        }
        .table-row {
            display: grid;
            grid-template-columns: 2fr 2fr 1fr 1fr;
            gap: 1px;
            padding: 12px;
            border-bottom: 1px solid #f0f0f0;
        }
        .table-row:last-child {
            border-bottom: none;
        }
        .more-records {
            text-align: center;
            padding: 16px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
        }
        .view-more-link {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
        }
        .view-more-link:hover {
            text-decoration: underline;
        }
        .status {
            color: #28a745;
            font-weight: 500;
        }
        .test-title {
            color: #333;
            margin-bottom: 16px;
        }
        .records-count {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>📋 测试记录显示验证</h1>
    
    <div class="test-section">
        <h2 class="test-title">PDQ-5 认知评估 <span class="records-count">共 5 次</span></h2>
        
        <div class="records-table">
            <div class="table-header">
                <div>开始日期</div>
                <div>结束日期</div>
                <div>测试用时（分钟）</div>
                <div>查看结果</div>
            </div>
            
            <!-- 只显示最近2条记录 -->
            <div class="table-row">
                <div>2024-01-20 14:30</div>
                <div>2024-01-20 14:45</div>
                <div>15分钟</div>
                <div><span class="status">查看结果</span></div>
            </div>
            
            <div class="table-row">
                <div>2024-01-19 09:15</div>
                <div>2024-01-19 09:28</div>
                <div>13分钟</div>
                <div><span class="status">查看结果</span></div>
            </div>
            
            <!-- 查看全部记录链接 -->
            <div class="more-records">
                <a href="/test-history?type=PDQ5" class="view-more-link">
                    查看全部 5 次记录
                </a>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2 class="test-title">掩蔽情感启动任务 <span class="records-count">共 3 次</span></h2>
        
        <div class="records-table">
            <div class="table-header">
                <div>开始日期</div>
                <div>结束日期</div>
                <div>测试用时（分钟）</div>
                <div>查看结果</div>
            </div>
            
            <!-- 只显示最近2条记录 -->
            <div class="table-row">
                <div>2024-01-21 16:20</div>
                <div>2024-01-21 16:32</div>
                <div>12分钟</div>
                <div><span class="status">查看结果</span></div>
            </div>
            
            <div class="table-row">
                <div>2024-01-18 11:45</div>
                <div>2024-01-18 11:57</div>
                <div>12分钟</div>
                <div><span class="status">查看结果</span></div>
            </div>
            
            <!-- 查看全部记录链接 -->
            <div class="more-records">
                <a href="/test-history?type=MaskedEmotion" class="view-more-link">
                    查看全部 3 次记录
                </a>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2 class="test-title">Hopkins 词汇学习测试 <span class="records-count">共 1 次</span></h2>
        
        <div class="records-table">
            <div class="table-header">
                <div>开始日期</div>
                <div>结束日期</div>
                <div>测试用时（分钟）</div>
                <div>查看结果</div>
            </div>
            
            <!-- 只有1条记录，不显示查看全部链接 -->
            <div class="table-row">
                <div>2024-01-17 10:30</div>
                <div>2024-01-17 10:50</div>
                <div>20分钟</div>
                <div><span class="status">查看结果</span></div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>✅ 修改验证结果</h2>
        <ul>
            <li><strong>✅ 记录显示限制</strong>：每个测试任务只显示最近2条记录</li>
            <li><strong>✅ 查看全部记录</strong>：当记录数 > 2 时显示"查看全部 X 次记录"链接</li>
            <li><strong>✅ URL参数支持</strong>：链接格式为 <code>/test-history?type=TestType</code></li>
            <li><strong>✅ 情绪掩蔽任务</strong>：已添加到系统中，类型为 <code>MaskedEmotion</code></li>
            <li><strong>✅ 自动过滤</strong>：测试历史页面会根据URL参数自动过滤对应类型的记录</li>
        </ul>
    </div>
    
    <script>
        // 模拟点击链接的行为
        document.querySelectorAll('.view-more-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const url = link.getAttribute('href');
                console.log('🔗 点击查看全部记录链接:', url);
                alert(`将跳转到: ${url}\n\n这将打开测试历史页面并自动过滤显示对应类型的所有记录。`);
            });
        });
        
        console.log('📋 测试记录显示验证页面已加载');
        console.log('✅ 每个测试任务只显示最近2条记录');
        console.log('✅ 超过2条记录时显示"查看全部记录"链接');
    </script>
</body>
</html>
