<template>
  <div class="test-history-container">
    <!-- 顶部导航 -->
    <header class="history-header">
      <button @click="$router.back()" class="back-button" aria-label="返回">
        <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
          <path d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"/>
        </svg>
      </button>
      <h1 class="page-title">测试记录</h1>
      <button
        @click="showFilters = !showFilters"
        class="filter-button"
        :class="{ active: hasActiveFilters }"
        :aria-expanded="showFilters"
        aria-label="筛选选项"
      >
        <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
          <path d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"/>
        </svg>
      </button>
    </header>

    <main class="history-content">
      <!-- 统计卡片 -->
      <section class="stats-section">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon completed">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
              </svg>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ completedTests }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon average">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ averageScore }}%</div>
              <div class="stat-label">平均得分</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon time">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ totalTestTime }}h</div>
              <div class="stat-label">总时长</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 筛选器 -->
      <section v-if="showFilters" class="filters-section">
        <div class="filters-card">
          <h3 class="filters-title">筛选条件</h3>
          
          <div class="filters-grid">
            <div class="filter-group">
              <label for="statusFilter" class="filter-label">状态</label>
              <select
                id="statusFilter"
                v-model="filters.status"
                class="filter-select"
              >
                <option value="">全部状态</option>
                <option value="completed">已完成</option>
                <option value="inProgress">进行中</option>
                <option value="pending">待开始</option>
                <option value="failed">失败</option>
              </select>
            </div>
            
            <div class="filter-group">
              <label for="typeFilter" class="filter-label">测试类型</label>
              <select
                id="typeFilter"
                v-model="filters.testType"
                class="filter-select"
              >
                <option value="">全部类型</option>
                <option v-for="type in availableTestTypes" :key="type" :value="type">
                  {{ getTestTypeName(type) }}
                </option>
              </select>
            </div>
            
            <div class="filter-group">
              <label for="dateFilter" class="filter-label">时间范围</label>
              <select
                id="dateFilter"
                v-model="filters.dateRange"
                class="filter-select"
              >
                <option value="">全部时间</option>
                <option value="today">今天</option>
                <option value="week">最近一周</option>
                <option value="month">最近一月</option>
                <option value="quarter">最近三月</option>
              </select>
            </div>
          </div>
          
          <div class="filters-actions">
            <button @click="resetFilters" class="btn btn-outline btn-sm">
              重置
            </button>
            <button @click="showFilters = false" class="btn btn-primary btn-sm">
              确定
            </button>
          </div>
        </div>
      </section>

      <!-- 测试记录列表 -->
      <section class="records-section">
        <div class="section-header">
          <h2 class="section-title">
            测试记录 ({{ filteredTests.length }})
          </h2>
          <div class="view-toggle">
            <button
              @click="viewMode = 'list'"
              :class="['toggle-btn', { active: viewMode === 'list' }]"
              aria-label="列表视图"
            >
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M2 3h12a1 1 0 010 2H2a1 1 0 010-2zm0 4h12a1 1 0 010 2H2a1 1 0 010-2zm0 4h12a1 1 0 010 2H2a1 1 0 010-2z"/>
              </svg>
            </button>
            <button
              @click="viewMode = 'grid'"
              :class="['toggle-btn', { active: viewMode === 'grid' }]"
              aria-label="网格视图"
            >
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M1 1h6v6H1V1zm8 0h6v6H9V1zM1 9h6v6H1V9zm8 0h6v6H9V9z"/>
              </svg>
            </button>
            <button
              @click="viewMode = 'grouped'"
              :class="['toggle-btn', { active: viewMode === 'grouped' }]"
              aria-label="按日期分组"
            >
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <path d="M3.5 0a.5.5 0 01.5.5V1h8V.5a.5.5 0 011 0V1h1a2 2 0 012 2v11a2 2 0 01-2 2H2a2 2 0 01-2-2V3a2 2 0 012-2h1V.5a.5.5 0 01.5 0zM2 2a1 1 0 00-1 1v1h14V3a1 1 0 00-1-1H2zM1 5v9a1 1 0 001 1h12a1 1 0 001-1V5H1z"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredTests.length === 0" class="empty-state">
          <div class="empty-icon">
            <svg width="64" height="64" viewBox="0 0 64 64" fill="currentColor" opacity="0.3">
              <path d="M32 8C18.745 8 8 18.745 8 32s10.745 24 24 24 24-10.745 24-24S45.255 8 32 8zm-4 34H24v-4h4v4zm0-8H24V18h4v8zm8 8h-4v-4h4v4zm0-8h-4V18h4v8z"/>
            </svg>
          </div>
          <h3 class="empty-title">暂无测试记录</h3>
          <p class="empty-description">
            {{ hasActiveFilters ? '没有符合筛选条件的记录' : '您还没有参加过任何测试' }}
          </p>
          <router-link to="/tests" class="btn btn-primary">
            开始测试
          </router-link>
        </div>

        <!-- 列表视图 -->
        <div v-else-if="viewMode === 'list'" class="tests-list">
          <div
            v-for="test in paginatedTests"
            :key="test.id"
            class="test-item"
          >
            <div class="test-main">
              <div class="test-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.1 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"/>
                </svg>
              </div>
              <div class="test-info">
                <h3 class="test-name">{{ test.testName }}</h3>
                <div class="test-meta">
                  <span class="test-date">{{ formatDate(test.startTime) }}</span>
                  <span v-if="test.duration" class="test-duration">
                    {{ formatDuration(test.duration) }}
                  </span>
                </div>
              </div>
            </div>
            
            <div class="test-result">
              <span
                :class="['status-badge', `status-${test.status}`]"
                :aria-label="`测试状态：${getStatusText(test.status)}`"
              >
                {{ getStatusText(test.status) }}
              </span>
              <span v-if="test.score !== undefined" class="test-score">
                {{ test.score }}分
              </span>
            </div>
            
            <div class="test-actions">
              <button
                v-if="test.status === 'completed'"
                @click="viewTestDetail(test)"
                class="view-result-btn"
              >
                📈 查看测试结果
              </button>
              <span v-else class="status-text">
                {{ getStatusText(test.status) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 网格视图 -->
        <div v-else-if="viewMode === 'grid'" class="tests-grid">
          <div
            v-for="test in paginatedTests"
            :key="test.id"
            class="test-card"
          >
            <div class="card-header">
              <div class="test-type-badge">
                {{ getTestTypeName(test.testType) }}
              </div>
              <span
                :class="['status-badge', `status-${test.status}`]"
                :aria-label="`测试状态：${getStatusText(test.status)}`"
              >
                {{ getStatusText(test.status) }}
              </span>
            </div>
            
            <div class="card-content">
              <h3 class="test-name">{{ test.testName }}</h3>
              <div class="test-meta">
                <div class="meta-item">
                  <svg width="14" height="14" viewBox="0 0 14 14" fill="currentColor">
                    <path d="M7 0C3.134 0 0 3.134 0 7s3.134 7 7 7 7-3.134 7-7-3.134-7-7-7zm0 12.6C3.685 12.6 1.4 10.315 1.4 7S3.685 1.4 7 1.4 12.6 3.685 12.6 7 10.315 12.6 7 12.6z"/>
                    <path d="M7.7 3.5H6.3v4.2l3.15 1.89.7-1.155L7.7 6.65V3.5z"/>
                  </svg>
                  {{ formatDate(test.startTime) }}
                </div>
                <div v-if="test.duration" class="meta-item">
                  <svg width="14" height="14" viewBox="0 0 14 14" fill="currentColor">
                    <path d="M7 0C3.134 0 0 3.134 0 7s3.134 7 7 7 7-3.134 7-7-3.134-7-7-7zm0 12.6C3.685 12.6 1.4 10.315 1.4 7S3.685 1.4 7 1.4 12.6 3.685 12.6 7 10.315 12.6 7 12.6z"/>
                  </svg>
                  {{ formatDuration(test.duration) }}
                </div>
              </div>
            </div>
            
            <div class="card-footer">
              <span v-if="test.score !== undefined" class="test-score">
                得分：{{ test.score }}分
              </span>
              <button
                v-if="test.status === 'completed'"
                @click="viewTestDetail(test)"
                class="view-detail-btn"
                aria-label="查看测试结果"
              >
                📈 查看测试结果
              </button>
              <span v-else class="status-text">
                {{ getStatusText(test.status) }}
              </span>
            </div>
          </div>
        </div>

        <!-- 按日期分组视图 -->
        <div v-else-if="viewMode === 'grouped'" class="tests-grouped">
          <div
            v-for="(records, date) in groupedTests"
            :key="date"
            class="date-group"
          >
            <!-- 日期头部 -->
            <div class="date-header" @click="toggleDateGroup(date)">
              <div class="date-info">
                <h3 class="date-title">{{ formatDateChinese(date) }}</h3>
                <span class="date-count">{{ records.length }} 次测试</span>
              </div>

              <div class="date-stats">
                <div class="completion-rate">
                  <span class="rate-text">完成率</span>
                  <span class="rate-value">{{ getDateCompletionRate(records) }}%</span>
                </div>

                <!-- 任务测试报告按钮 - 只在完成率100%时显示 -->
                <button
                  v-if="getDateCompletionRate(records) === 100"
                  @click.stop="viewDateReport(date, records)"
                  class="task-report-btn"
                >
                  📊 查看测试报告
                </button>

                <button class="expand-btn" :class="{ expanded: expandedDate === date }">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                    <path d="M1.646 4.646a.5.5 0 01.708 0L8 10.293l5.646-5.647a.5.5 0 01.708.708l-6 6a.5.5 0 01-.708 0l-6-6a.5.5 0 010-.708z"/>
                  </svg>
                </button>
              </div>
            </div>

            <!-- 测试记录列表 -->
            <div v-if="expandedDate === date" class="date-records">
              <div
                v-for="record in records"
                :key="record.id"
                class="record-item"
              >
                <div class="record-main">
                  <div class="record-icon">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                  <div class="record-info">
                    <h4 class="record-name">{{ record.testName }}</h4>
                    <div class="record-meta">
                      <span class="record-time">{{ formatTime(record.startTime) }}</span>
                      <span v-if="record.duration" class="record-duration">
                        {{ formatDuration(record.duration) }}
                      </span>
                      <span v-if="record.score !== undefined" class="record-score">
                        {{ record.score }}分
                      </span>
                    </div>
                  </div>
                </div>

                <div class="record-actions">
                  <button
                    v-if="record.status === 'completed'"
                    @click="viewTestDetail(record)"
                    class="view-result-btn"
                  >
                    📈 查看测试结果
                  </button>
                  <span v-else class="status-text">
                    {{ getStatusText(record.status) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="pagination">
          <button
            @click="currentPage--"
            :disabled="currentPage === 1"
            class="pagination-btn"
            aria-label="上一页"
          >
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M11.354 1.646a.5.5 0 010 .708L5.707 8l5.647 5.646a.5.5 0 01-.708.708l-6-6a.5.5 0 010-.708l6-6a.5.5 0 01.708 0z"/>
            </svg>
          </button>
          
          <div class="pagination-info">
            第 {{ currentPage }} 页，共 {{ totalPages }} 页
          </div>
          
          <button
            @click="currentPage++"
            :disabled="currentPage === totalPages"
            class="pagination-btn"
            aria-label="下一页"
          >
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M4.646 1.646a.5.5 0 000 .708L10.293 8 4.646 14.354a.5.5 0 10.708.708l6-6a.5.5 0 000-.708l-6-6a.5.5 0 00-.708 0z"/>
            </svg>
          </button>
        </div>
      </section>
    </main>

    <!-- 测试报告模态框 -->
    <div v-if="showReportModal" class="modal-overlay" @click="closeReportModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2 class="modal-title">{{ currentReport?.testName }} 测试报告</h2>
          <button @click="closeReportModal" class="modal-close" aria-label="关闭">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </button>
        </div>

        <div class="modal-body">
          <div v-if="currentReport" class="report-content">
            <!-- 总体得分 -->
            <div class="score-section">
              <div class="score-circle">
                <div class="score-value">{{ currentReport.score }}</div>
                <div class="score-label">总分</div>
              </div>
              <div class="score-description">
                <h3>测试表现</h3>
                <p>{{ getScoreDescription(currentReport.score) }}</p>
              </div>
            </div>

            <!-- 详细分析 -->
            <div class="analysis-section">
              <h3>详细分析</h3>
              <p>{{ currentReport.analysis }}</p>
            </div>

            <!-- 建议 -->
            <div v-if="currentReport.recommendations?.length" class="recommendations-section">
              <h3>建议</h3>
              <ul class="recommendations-list">
                <li v-for="(rec, index) in currentReport.recommendations" :key="index">
                  {{ rec }}
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button @click="closeReportModal" class="btn btn-primary">
            确定
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { getUserTestRecords, generateTestReport, getGroupedTestRecords } from '@/utils/mockData'
import type { TestRecord } from '@/types/user'
import dayjs from 'dayjs'

const authStore = useAuthStore()
const route = useRoute()

// 响应式数据
const showFilters = ref(false)
const viewMode = ref<'list' | 'grid' | 'grouped'>('list')
const currentPage = ref(1)
const pageSize = ref(10)

const tests = ref<TestRecord[]>([])
const groupedTests = ref<Record<string, TestRecord[]>>({})
const expandedDate = ref<string | null>(null) // 展开的日期分组
const showReportModal = ref(false) // 显示测试报告模态框
const currentReport = ref<any>(null) // 当前查看的报告数据

const filters = ref({
  status: '',
  testType: '',
  dateRange: ''
})

// 计算属性
const user = computed(() => authStore.user)

const completedTests = computed(() => 
  tests.value.filter(test => test.status === 'completed').length
)

const averageScore = computed(() => {
  const completedWithScore = tests.value.filter(test => 
    test.status === 'completed' && test.score !== undefined
  )
  if (completedWithScore.length === 0) return 0
  
  const total = completedWithScore.reduce((sum, test) => sum + (test.score || 0), 0)
  return Math.round(total / completedWithScore.length)
})

const totalTestTime = computed(() => {
  const totalSeconds = tests.value
    .filter(test => test.duration)
    .reduce((sum, test) => sum + (test.duration || 0), 0)
  return Math.round(totalSeconds / 3600 * 10) / 10
})

const availableTestTypes = computed(() => {
  const types = new Set(tests.value.map(test => test.testType))
  return Array.from(types)
})

const hasActiveFilters = computed(() => {
  return filters.value.status || filters.value.testType || filters.value.dateRange
})

const filteredTests = computed(() => {
  let filtered = [...tests.value]
  
  // 状态筛选
  if (filters.value.status) {
    filtered = filtered.filter(test => test.status === filters.value.status)
  }
  
  // 类型筛选
  if (filters.value.testType) {
    filtered = filtered.filter(test => test.testType === filters.value.testType)
  }
  
  // 时间筛选
  if (filters.value.dateRange) {
    const now = dayjs()
    filtered = filtered.filter(test => {
      const testDate = dayjs(test.startTime)
      switch (filters.value.dateRange) {
        case 'today':
          return testDate.isSame(now, 'day')
        case 'week':
          return testDate.isAfter(now.subtract(7, 'day'))
        case 'month':
          return testDate.isAfter(now.subtract(1, 'month'))
        case 'quarter':
          return testDate.isAfter(now.subtract(3, 'month'))
        default:
          return true
      }
    })
  }
  
  // 按时间倒序排列
  return filtered.sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime())
})

const totalPages = computed(() => {
  return Math.ceil(filteredTests.value.length / pageSize.value)
})

const paginatedTests = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredTests.value.slice(start, end)
})

// 方法
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('MM月DD日 HH:mm')
}

const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}分${remainingSeconds}秒`
}

const getStatusText = (status: string) => {
  const statusMap = {
    pending: '待开始',
    inProgress: '进行中',
    completed: '已完成',
    failed: '失败'
  }
  return statusMap[status as keyof typeof statusMap] || status
}

const getTestTypeName = (testType: string) => {
  const typeMap = {
    PDQ5: 'PDQ-5',
    Hopkins: 'Hopkins词汇学习',
    NBack: '顺背/倒背测试',
    Stroop: 'Stroop色词测试',
    TrailMaking: '连线测试',
    VerbalFluency: '词语流畅性',
    CPT: '持续性操作测试',
    DSST: 'DSST数字符号',
    MaskedEmotion: '掩蔽情感启动任务'
  }
  return typeMap[testType as keyof typeof typeMap] || testType
}

const resetFilters = () => {
  filters.value = {
    status: '',
    testType: '',
    dateRange: ''
  }
  currentPage.value = 1
}

// 查看测试结果（与Home页面保持一致）
const viewTestDetail = (test: TestRecord) => {
  if (test.status !== 'completed') {
    return
  }

  const report = generateTestReport(test)
  if (!report) {
    return
  }

  currentReport.value = report
  showReportModal.value = true
}

// 关闭报告模态框
const closeReportModal = () => {
  showReportModal.value = false
  currentReport.value = null
}

// 获取得分描述
const getScoreDescription = (score: number) => {
  if (score >= 90) return '优秀！您的认知能力表现非常出色。'
  if (score >= 80) return '良好！您的认知能力表现不错。'
  if (score >= 70) return '一般！您的认知能力表现中等。'
  if (score >= 60) return '需要改进！建议加强相关训练。'
  return '需要关注！建议咨询专业医生。'
}

// 切换日期分组展开状态
const toggleDateGroup = (date: string) => {
  expandedDate.value = expandedDate.value === date ? null : date
}

// 格式化中文日期
const formatDateChinese = (dateString: string): string => {
  return dayjs(dateString).format('YYYY年MM月DD日')
}

// 格式化时间
const formatTime = (dateString: string): string => {
  return dayjs(dateString).format('HH:mm')
}

// 计算日期完成率
const getDateCompletionRate = (records: TestRecord[]) => {
  const completed = records.filter(record => record.status === 'completed').length
  return records.length > 0 ? Math.round((completed / records.length) * 100) : 0
}

// 查看日期报告（任务级别报告）
const viewDateReport = (date: string, records: TestRecord[]) => {
  const completedRecords = records.filter(record => record.status === 'completed')

  if (completedRecords.length === 0) {
    return
  }

  // 计算综合得分（该日期所有测试的平均分）
  const totalScore = completedRecords.reduce((sum, record) => sum + (record.score || 0), 0)
  const averageScore = Math.round(totalScore / completedRecords.length)

  // 生成任务级别的综合报告
  const report = {
    isTaskReport: true,
    taskName: `${formatDateChinese(date)} 测试任务`,
    testName: `${formatDateChinese(date)} 测试任务`,
    testCount: completedRecords.length,
    score: averageScore,
    percentile: Math.floor(Math.random() * 40) + 60,
    analysis: `基于 ${completedRecords.length} 个测试项目的综合分析显示，您在${formatDateChinese(date)}的整体表现${averageScore > 80 ? '优秀' : averageScore > 70 ? '良好' : '中等'}。各项认知能力指标表现均衡。`,
    recommendations: [
      '恭喜完成当日的认知能力评估',
      '建议定期进行复测以跟踪认知能力变化',
      '可以针对薄弱环节进行专项训练',
      '保持健康的生活方式有助于认知能力维护'
    ]
  }

  currentReport.value = report
  showReportModal.value = true
}

const loadTestHistory = () => {
  if (user.value) {
    tests.value = getUserTestRecords(user.value.id)
    groupedTests.value = getGroupedTestRecords(user.value.id)
  }
}

// 初始化过滤器（从URL参数）
const initializeFilters = () => {
  const typeParam = route.query.type as string
  if (typeParam) {
    filters.value.testType = typeParam
  }
}

// 生命周期
onMounted(() => {
  initializeFilters()
  loadTestHistory()
})
</script>

<style lang="scss" scoped>
.test-history-container {
  min-height: 100vh;
  background: var(--color-background);
}

.history-header {
  background: var(--color-card);
  border-bottom: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-md $spacing-lg;
}

.back-button, .filter-button {
  background: none;
  border: none;
  color: var(--color-muted-foreground);
  cursor: pointer;
  padding: $spacing-sm;
  border-radius: var(--radius);
  transition: all 0.2s ease;
  
  &:hover {
    color: var(--color-foreground);
    background: var(--color-muted);
  }
  
  &.active {
    color: var(--color-primary);
  }
}

.page-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--color-foreground);
}

.history-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: $spacing-xl $spacing-lg;
}

.stats-section {
  margin-bottom: $spacing-2xl;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-lg;
}

.stat-card {
  background: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: $spacing-lg;
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.completed {
    background: oklch(from var(--color-primary) l c h / 0.1);
    color: var(--color-primary);
  }
  
  &.average {
    background: oklch(from var(--color-secondary) l c h / 0.1);
    color: var(--color-secondary);
  }
  
  &.time {
    background: oklch(from var(--color-accent) l c h / 0.1);
    color: var(--color-accent);
  }
}

.stat-number {
  font-size: $font-size-2xl;
  font-weight: 700;
  color: var(--color-foreground);
  margin-bottom: $spacing-xs;
}

.stat-label {
  font-size: $font-size-sm;
  color: var(--color-muted-foreground);
}

.filters-section {
  margin-bottom: $spacing-xl;
}

.filters-card {
  background: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: $spacing-lg;
}

.filters-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--color-foreground);
  margin-bottom: $spacing-lg;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: $spacing-md;
  margin-bottom: $spacing-lg;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: $spacing-sm;
}

.filter-label {
  font-size: $font-size-sm;
  font-weight: 500;
  color: var(--color-foreground);
}

.filter-select {
  padding: $spacing-sm $spacing-md;
  border: 1px solid var(--color-border);
  border-radius: var(--radius);
  background: var(--color-input);
  font-size: $font-size-base;
  
  &:focus {
    outline: none;
    border-color: var(--color-primary);
  }
}

.filters-actions {
  display: flex;
  gap: $spacing-md;
  justify-content: flex-end;
}

.btn-sm {
  padding: $spacing-sm $spacing-md;
  font-size: $font-size-sm;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $spacing-lg;
}

.section-title {
  font-size: $font-size-xl;
  font-weight: 600;
  color: var(--color-foreground);
}

.view-toggle {
  display: flex;
  gap: $spacing-xs;
}

.toggle-btn {
  background: none;
  border: 1px solid var(--color-border);
  color: var(--color-muted-foreground);
  padding: $spacing-sm;
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    color: var(--color-foreground);
    background: var(--color-muted);
  }
  
  &.active {
    background: var(--color-primary);
    color: var(--color-primary-foreground);
    border-color: var(--color-primary);
  }
}

.empty-state {
  text-align: center;
  padding: $spacing-2xl;
  background: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
}

.empty-icon {
  margin-bottom: $spacing-lg;
  color: var(--color-muted-foreground);
}

.empty-title {
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--color-foreground);
  margin-bottom: $spacing-sm;
}

.empty-description {
  color: var(--color-muted-foreground);
  margin-bottom: $spacing-lg;
}

.tests-list {
  background: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.test-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-lg;
  border-bottom: 1px solid var(--color-border);
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background: var(--color-muted);
  }
  
  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: -2px;
  }
}

.test-main {
  display: flex;
  align-items: center;
  gap: $spacing-md;
  flex: 1;
}

.test-icon {
  width: 40px;
  height: 40px;
  background: var(--color-muted);
  border-radius: var(--radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-muted-foreground);
  flex-shrink: 0;
}

.test-name {
  font-size: $font-size-base;
  font-weight: 500;
  color: var(--color-foreground);
  margin-bottom: $spacing-xs;
}

.test-meta {
  display: flex;
  gap: $spacing-md;
  font-size: $font-size-sm;
  color: var(--color-muted-foreground);
}

.test-result {
  display: flex;
  align-items: center;
  gap: $spacing-sm;
}

.status-badge {
  padding: $spacing-xs $spacing-sm;
  border-radius: var(--radius-sm);
  font-size: $font-size-xs;
  font-weight: 500;
  
  &.status-completed {
    background: oklch(from var(--color-primary) l c h / 0.1);
    color: var(--color-primary);
  }
  
  &.status-inProgress {
    background: oklch(from var(--color-secondary) l c h / 0.1);
    color: var(--color-secondary);
  }
  
  &.status-pending {
    background: var(--color-muted);
    color: var(--color-muted-foreground);
  }
  
  &.status-failed {
    background: oklch(from var(--color-destructive) l c h / 0.1);
    color: var(--color-destructive);
  }
}

.test-score {
  font-weight: 600;
  color: var(--color-primary);
}

.test-actions {
  color: var(--color-muted-foreground);
  margin-left: $spacing-md;
}

.tests-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: $spacing-lg;
}

.test-card {
  background: var(--color-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: $spacing-lg;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--color-primary);
  }
  
  &:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-md;
}

.test-type-badge {
  background: var(--color-muted);
  color: var(--color-muted-foreground);
  padding: $spacing-xs $spacing-sm;
  border-radius: var(--radius-sm);
  font-size: $font-size-xs;
  font-weight: 500;
}

.card-content {
  margin-bottom: $spacing-lg;
}

.test-name {
  font-size: $font-size-lg;
  font-weight: 600;
  color: var(--color-foreground);
  margin-bottom: $spacing-sm;
}

.test-meta {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
  font-size: $font-size-sm;
  color: var(--color-muted-foreground);
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: $spacing-md;
  border-top: 1px solid var(--color-border);
}

.view-detail-btn {
  background: none;
  border: none;
  color: var(--color-primary);
  font-size: $font-size-sm;
  font-weight: 500;
  cursor: pointer;
  
  &:hover {
    text-decoration: underline;
  }
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $spacing-lg;
  margin-top: $spacing-xl;
  padding: $spacing-lg;
}

.pagination-btn {
  background: var(--color-card);
  border: 1px solid var(--color-border);
  color: var(--color-foreground);
  padding: $spacing-sm;
  border-radius: var(--radius);
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    background: var(--color-muted);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.pagination-info {
  font-size: $font-size-sm;
  color: var(--color-muted-foreground);
}

// 响应式设计
@media (max-width: $mobile) {
  .history-content {
    padding: $spacing-lg $spacing-md;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .filters-grid {
    grid-template-columns: 1fr;
  }
  
  .test-item {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;
  }
  
  .test-result {
    align-self: flex-end;
  }
  
  .tests-grid {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-md;
  }
}

// 测试报告模态框样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: var(--color-background);
  border-radius: 16px;
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 0;
  border-bottom: 1px solid var(--color-border);
  margin-bottom: 24px;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
}

.modal-close {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    background: var(--color-background-soft);
    color: var(--color-text-primary);
  }
}

.modal-body {
  padding: 0 24px 24px;
}

.score-section {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 32px;
  padding: 24px;
  background: var(--color-background-soft);
  border-radius: 12px;
}

.score-circle {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
  color: white;
  flex-shrink: 0;
}

.score-value {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
}

.score-label {
  font-size: 12px;
  opacity: 0.9;
  margin-top: 2px;
}

.score-description h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 8px 0;
}

.score-description p {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin: 0;
  line-height: 1.5;
}

.analysis-section,
.recommendations-section {
  margin-bottom: 24px;
}

.analysis-section h3,
.recommendations-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 12px 0;
}

.analysis-section p {
  font-size: 14px;
  color: var(--color-text-secondary);
  line-height: 1.6;
  margin: 0;
}

.recommendations-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recommendations-list li {
  font-size: 14px;
  color: var(--color-text-secondary);
  line-height: 1.5;
  padding: 8px 0;
  border-bottom: 1px solid var(--color-border);
  position: relative;
  padding-left: 20px;

  &:before {
    content: '•';
    color: var(--color-primary);
    position: absolute;
    left: 0;
    font-weight: bold;
  }

  &:last-child {
    border-bottom: none;
  }
}

.modal-footer {
  padding: 0 24px 24px;
  display: flex;
  justify-content: flex-end;
}

.view-result-btn {
  background: var(--color-primary);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: var(--color-primary-dark);
    transform: translateY(-1px);
  }
}

.status-text {
  font-size: 14px;
  color: var(--color-text-secondary);
  font-style: italic;
}

// 按日期分组视图样式
.tests-grouped {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.date-group {
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 12px;
  overflow: hidden;
}

.date-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: var(--color-background-mute);
  }
}

.date-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.date-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0;
}

.date-count {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.date-stats {
  display: flex;
  align-items: center;
  gap: 12px;
}

.completion-rate {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.rate-text {
  font-size: 12px;
  color: var(--color-text-secondary);
}

.rate-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-primary);
}

.task-report-btn {
  background: var(--color-secondary);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: var(--color-secondary-dark);
    transform: translateY(-1px);
  }
}

.expand-btn {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  transform: rotate(0deg);

  &.expanded {
    transform: rotate(180deg);
  }

  &:hover {
    background: var(--color-background-mute);
    color: var(--color-text-primary);
  }
}

.date-records {
  border-top: 1px solid var(--color-border);
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.record-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: var(--color-background-soft);
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    background: var(--color-background-mute);
  }
}

.record-main {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.record-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: var(--color-primary);
  color: white;
  flex-shrink: 0;
}

.record-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.record-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary);
  margin: 0;
}

.record-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: var(--color-text-secondary);
}

.record-time,
.record-duration,
.record-score {
  display: flex;
  align-items: center;
  gap: 4px;
}

.record-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>