{"name": "cognition-frontend", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "format": "prettier --write src/"}, "dependencies": {"dayjs": "^1.11.0", "lodash-es": "^4.17.21", "pinia": "^2.1.0", "uuid": "^9.0.0", "vant": "^4.8.0", "vue": "^3.4.0", "vue-router": "^4.2.0"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^5.0.0", "eslint": "^8.55.0", "prettier": "^3.1.0", "sass": "^1.89.2", "sass-embedded": "^1.89.2", "typescript": "^5.3.0", "vite": "^5.0.0", "vue-tsc": "^1.8.25"}}