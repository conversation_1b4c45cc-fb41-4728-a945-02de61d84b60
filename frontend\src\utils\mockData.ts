// 模拟数据工具
import type { User, TestRecord } from '@/types/user'

// 模拟网络延迟
export const mockDelay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms))

// 生成患者编号
export const generatePatientNumber = (): string => {
  const timestamp = Date.now().toString().slice(-6)
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `P${timestamp}${random}`
}

// 模拟用户数据
export const mockUsers: User[] = [
  {
    id: 'user_001',
    patientNumber: 'P123456001',
    phone: '13800138000',
    name: '张三',
    education: '本科',
    gender: 'male',
    contactPhone: '13800138000',
    dominantHand: 'right',
    isColorBlind: false,
    idCard: '',
    avatar: '/avatars/default-male.png',
    createdAt: '2024-01-15T10:30:00Z',
    lastLoginAt: '2024-01-20T14:22:00Z'
  },
  {
    id: 'user_002',
    patientNumber: 'P123456002',
    phone: '13800138001',
    name: '李四',
    education: '硕士',
    gender: 'female',
    contactPhone: '13800138001',
    dominantHand: 'right',
    isColorBlind: false,
    idCard: '110101199002022345',
    avatar: '/avatars/default-female.png',
    createdAt: '2024-01-16T09:15:00Z',
    lastLoginAt: '2024-01-19T16:45:00Z'
  },
  {
    id: 'user_003',
    patientNumber: 'P123456003',
    phone: '13800138002',
    name: '王五',
    education: '高中',
    gender: 'male', 
    contactPhone: '13800138002',
    dominantHand: 'left',
    isColorBlind: true,
    idCard: '110101199003033456',
    avatar: '/avatars/default-male.png',
    createdAt: '2024-01-16T11:20:00Z',
    lastLoginAt: '2024-01-18T13:10:00Z'
  }
]

// 模拟测试记录数据 - 包含多次测试记录
export const mockTestRecords: TestRecord[] = [
  // PDQ-5 量表测试 - 多次测试记录
  {
    id: 'test_001',
    userId: 'user_001',
    testType: 'PDQ5',
    testName: 'PDQ-5 量表测试',
    startTime: '2024-01-20T10:00:00Z',
    endTime: '2024-01-20T10:15:00Z',
    status: 'completed',
    score: 85,
    duration: 900,
    results: {
      memoryScore: 85,
      attentionScore: 82,
      thinkingScore: 87,
      overallPercentile: 78,
      recommendations: ['建议加强记忆训练', '保持良好的注意力习惯']
    },
    isRetesting: false
  },
  {
    id: 'test_001_retest1',
    userId: 'user_001',
    testType: 'PDQ5',
    testName: 'PDQ-5 量表测试',
    startTime: '2024-01-25T14:30:00Z',
    endTime: '2024-01-25T14:42:00Z',
    status: 'completed',
    score: 89,
    duration: 720,
    results: {
      memoryScore: 88,
      attentionScore: 85,
      thinkingScore: 92,
      overallPercentile: 82,
      recommendations: ['记忆能力有所提升', '继续保持良好状态']
    },
    isRetesting: true
  },
  {
    id: 'test_001_retest2',
    userId: 'user_001',
    testType: 'PDQ5',
    testName: 'PDQ-5 量表测试',
    startTime: '2024-02-01T09:15:00Z',
    endTime: '2024-02-01T09:25:00Z',
    status: 'completed',
    score: 92,
    duration: 600,
    results: {
      memoryScore: 91,
      attentionScore: 88,
      thinkingScore: 95,
      overallPercentile: 85,
      recommendations: ['认知能力持续改善', '表现优秀']
    },
    isRetesting: true
  },

  // Hopkins 词汇学习测试 - 多次测试记录
  {
    id: 'test_002',
    userId: 'user_001',
    testType: 'Hopkins',
    testName: 'Hopkins 词汇学习测试',
    startTime: '2024-01-19T14:30:00Z',
    endTime: '2024-01-19T14:50:00Z',
    status: 'completed',
    score: 78,
    duration: 1200,
    results: {
      immediateRecall: 80,
      delayedRecall: 76,
      learningEfficiency: 85,
      retentionRate: 73,
      overallPercentile: 65,
      recommendations: ['建议进行重复学习练习', '加强长期记忆巩固']
    },
    isRetesting: false
  },
  {
    id: 'test_002_retest1',
    userId: 'user_001',
    testType: 'Hopkins',
    testName: 'Hopkins 词汇学习测试',
    startTime: '2024-01-26T16:00:00Z',
    endTime: '2024-01-26T16:18:00Z',
    status: 'completed',
    score: 82,
    duration: 1080,
    results: {
      immediateRecall: 84,
      delayedRecall: 80,
      learningEfficiency: 88,
      retentionRate: 76,
      overallPercentile: 70,
      recommendations: ['记忆能力有所提升', '继续练习延迟回忆']
    },
    isRetesting: true
  },

  // Stroop 色词测试 - 多次测试记录
  {
    id: 'test_003',
    userId: 'user_001',
    testType: 'Stroop',
    testName: 'Stroop 色词测试',
    startTime: '2024-01-21T16:00:00Z',
    endTime: '2024-01-21T16:08:00Z',
    status: 'completed',
    score: 92,
    duration: 480,
    results: {
      reactionTime: 650,
      accuracy: 94,
      interferenceControl: 88,
      overallPercentile: 82,
      recommendations: ['优秀的抑制控制能力', '继续保持专注训练']
    },
    isRetesting: false
  },
  {
    id: 'test_003_retest1',
    userId: 'user_001',
    testType: 'Stroop',
    testName: 'Stroop 色词测试',
    startTime: '2024-01-28T10:30:00Z',
    endTime: '2024-01-28T10:37:00Z',
    status: 'completed',
    score: 95,
    duration: 420,
    results: {
      reactionTime: 580,
      accuracy: 96,
      interferenceControl: 92,
      overallPercentile: 88,
      recommendations: ['反应时间显著改善', '抑制控制能力优秀']
    },
    isRetesting: true
  },

  // N-back工作记忆测试 - 多次测试记录
  {
    id: 'test_004',
    userId: 'user_001',
    testType: 'NBack',
    testName: 'N-back工作记忆测试',
    startTime: '2024-01-18T09:15:00Z',
    endTime: '2024-01-18T09:40:00Z',
    status: 'completed',
    score: 88,
    duration: 1500,
    results: {
      forwardSpan: 7,
      backwardSpan: 6,
      workingMemoryCapacity: 85,
      processingSpeed: 82,
      overallPercentile: 75,
      recommendations: ['工作记忆表现良好', '可适当增加复杂任务练习']
    },
    isRetesting: false
  },
  {
    id: 'test_004_retest1',
    userId: 'user_001',
    testType: 'NBack',
    testName: 'N-back工作记忆测试',
    startTime: '2024-01-24T15:20:00Z',
    endTime: '2024-01-24T15:42:00Z',
    status: 'completed',
    score: 91,
    duration: 1320,
    results: {
      forwardSpan: 8,
      backwardSpan: 7,
      workingMemoryCapacity: 88,
      processingSpeed: 85,
      overallPercentile: 80,
      recommendations: ['工作记忆能力提升明显', '继续保持训练强度']
    },
    isRetesting: true
  },

  // 连线测试 - 多次测试记录
  {
    id: 'test_005',
    userId: 'user_001',
    testType: 'TrailMaking',
    testName: '连线测试',
    startTime: '2024-01-22T11:00:00Z',
    endTime: '2024-01-22T11:12:00Z',
    status: 'completed',
    score: 86,
    duration: 720,
    results: {
      trailATime: 45,
      trailBTime: 95,
      executiveFunction: 84,
      processingSpeed: 88,
      overallPercentile: 72,
      recommendations: ['执行功能表现良好', '可进行更复杂的认知训练']
    },
    isRetesting: false
  },
  {
    id: 'test_005_retest1',
    userId: 'user_001',
    testType: 'TrailMaking',
    testName: '连线测试',
    startTime: '2024-01-29T14:15:00Z',
    endTime: '2024-01-29T14:25:00Z',
    status: 'completed',
    score: 90,
    duration: 600,
    results: {
      trailATime: 38,
      trailBTime: 82,
      executiveFunction: 89,
      processingSpeed: 92,
      overallPercentile: 78,
      recommendations: ['执行功能持续改善', '处理速度显著提升']
    },
    isRetesting: true
  },

  // 词语流畅性测试 - 多次测试记录
  {
    id: 'test_006',
    userId: 'user_001',
    testType: 'VerbalFluency',
    testName: '词语流畅性测试',
    startTime: '2024-01-23T13:30:00Z',
    endTime: '2024-01-23T13:45:00Z',
    status: 'completed',
    score: 83,
    duration: 900,
    results: {
      categoryFluency: 85,
      letterFluency: 81,
      semanticMemory: 84,
      executiveControl: 82,
      overallPercentile: 68,
      recommendations: ['语义流畅性表现良好', '可加强字母流畅性练习']
    },
    isRetesting: false
  },

  // 持续性操作测试 - 单次记录
  {
    id: 'test_007',
    userId: 'user_001',
    testType: 'CPT',
    testName: '持续性操作测试',
    startTime: '2024-01-17T10:45:00Z',
    endTime: '2024-01-17T11:00:00Z',
    status: 'completed',
    score: 87,
    duration: 900,
    results: {
      attentionSpan: 89,
      vigilance: 85,
      responseConsistency: 88,
      omissionErrors: 3,
      overallPercentile: 74,
      recommendations: ['注意力持续性良好', '反应一致性优秀']
    },
    isRetesting: false
  },

  // DSST数字符号转换 - 单次记录
  {
    id: 'test_008',
    userId: 'user_001',
    testType: 'DSST',
    testName: 'DSST数字符号转换测试',
    startTime: '2024-01-16T16:20:00Z',
    endTime: '2024-01-16T16:35:00Z',
    status: 'completed',
    score: 79,
    duration: 900,
    results: {
      processingSpeed: 82,
      workingMemory: 76,
      visualMotorCoordination: 80,
      learningEfficiency: 78,
      overallPercentile: 62,
      recommendations: ['处理速度中等', '建议加强视觉运动协调训练']
    },
    isRetesting: false
  }
]

// 获取用户的测试记录
export const getUserTestRecords = (userId: string): TestRecord[] => {
  const userRecords = mockTestRecords.filter(record => record.userId === userId)

  // 如果用户没有测试记录，返回示例数据（用于演示）
  if (userRecords.length === 0) {
    return mockTestRecords.filter(record => record.userId === 'user_001')
      .map(record => ({ ...record, userId })) // 更新用户ID以匹配当前用户
  }

  return userRecords
}

// 按日期分组测试记录
export const getGroupedTestRecords = (userId: string): Record<string, TestRecord[]> => {
  const records = getUserTestRecords(userId)
  const grouped: Record<string, TestRecord[]> = {}

  records.forEach(record => {
    const date = record.startTime.split('T')[0]
    if (!grouped[date]) {
      grouped[date] = []
    }
    grouped[date].push(record)
  })

  // 按日期倒序排列
  const sortedKeys = Object.keys(grouped).sort((a, b) => b.localeCompare(a))
  const sortedGrouped: Record<string, TestRecord[]> = {}
  sortedKeys.forEach(key => {
    sortedGrouped[key] = grouped[key].sort((a, b) =>
      new Date(b.startTime).getTime() - new Date(a.startTime).getTime()
    )
  })

  return sortedGrouped
}

// 获取特定测试类型的所有测试记录
export const getTestRecordsByType = (userId: string, testType: string): TestRecord[] => {
  const records = getUserTestRecords(userId)
  return records
    .filter(record => record.testType === testType)
    .sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime())
}

// 获取测试类型的统计信息
export const getTestTypeStats = (userId: string, testType: string) => {
  const records = getTestRecordsByType(userId, testType)
  const completedRecords = records.filter(record => record.status === 'completed')

  if (completedRecords.length === 0) {
    return {
      totalTests: 0,
      completedTests: 0,
      averageScore: 0,
      bestScore: 0,
      latestTest: null,
      improvement: 0
    }
  }

  const scores = completedRecords.map(record => record.score || 0)
  const averageScore = Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length)
  const bestScore = Math.max(...scores)
  const latestTest = completedRecords[0]

  // 计算改善程度（最新成绩与首次成绩的差值）
  const firstScore = completedRecords[completedRecords.length - 1]?.score || 0
  const latestScore = latestTest?.score || 0
  const improvement = latestScore - firstScore

  return {
    totalTests: records.length,
    completedTests: completedRecords.length,
    averageScore,
    bestScore,
    latestTest,
    improvement
  }
}

// 生成测试报告数据
export const generateTestReport = (record: TestRecord) => {
  if (!record.results || record.status !== 'completed') {
    return null
  }
  
  return {
    id: record.id,
    testName: record.testName,
    testType: record.testType,
    completedAt: record.endTime,
    duration: record.duration,
    score: record.score,
    percentile: record.results.overallPercentile || 0,
    detailedResults: record.results,
    recommendations: record.results.recommendations || [],
    interpretation: getScoreInterpretation(record.score || 0),
    comparisonData: generateComparisonData(record.testType, record.score || 0)
  }
}

// 分数解释
const getScoreInterpretation = (score: number): string => {
  if (score >= 90) return '优秀 - 表现非常出色'
  if (score >= 80) return '良好 - 表现高于平均水平'
  if (score >= 70) return '中等 - 表现处于平均水平'
  if (score >= 60) return '偏低 - 建议加强训练'
  return '需要改善 - 建议寻求专业指导'
}

// 生成对比数据
const generateComparisonData = (testType: string, score: number) => {
  // 模拟同龄人平均分数据
  const averageScores: Record<string, number> = {
    'PDQ5': 75,
    'Hopkins': 72,
    'Stroop': 78,
    'NBack': 70,
    'TrailMaking': 74,
    'VerbalFluency': 76,
    'CPT': 73,
    'DSST': 77
  }
  
  const average = averageScores[testType] || 75
  const difference = score - average
  
  return {
    userScore: score,
    averageScore: average,
    difference: difference,
    percentageDiff: Math.round((difference / average) * 100),
    isAboveAverage: difference > 0
  }
}

// 教育程度选项
export const educationOptions = [
  { label: '小学', value: '小学' },
  { label: '初中', value: '初中' },
  { label: '高中', value: '高中' },
  { label: '大专', value: '大专' },
  { label: '本科', value: '本科' },
  { label: '硕士', value: '硕士' },
  { label: '博士', value: '博士' }
]

// 测试地点选项
export const testLocationOptions = [
  { label: '测试中心1', value: '测试中心1' },
  { label: '测试中心2', value: '测试中心2' },
  { label: '测试中心3', value: '测试中心3' },
  { label: '测试中心4', value: '测试中心4' },
  { label: '测试中心5', value: '测试中心5' }
]

// 测试任务接口
export interface TestTask {
  id: string
  type: string
  name: string
  description: string
  estimatedDuration: number // 预估时长(分钟)
  difficulty: 'easy' | 'medium' | 'hard'
  category: string
  icon: string
  isAvailable: boolean
  isMainTask?: boolean // 是否为主要任务
  requiresMainCompletion?: boolean // 是否需要完成主要任务后才能进行
  order: number // 排序
  briefDescription?: string // 简要说明
  fullDescription?: string // 详细说明
  instructions?: string[] // 测试指导
}

// 大任务套件接口
export interface TestSuite {
  id: string
  name: string
  description: string
  icon: string
  tasks: string[] // 包含的测试任务ID
  estimatedDuration: number
  isCompleted: boolean
  completionRate: number // 完成率 0-100
  suiteNumber: string // 测试套件编号
  createdDate: string // 创建日期
  lastUpdated?: string // 最后更新时间
  status: 'not_started' | 'in_progress' | 'completed' // 套件状态
}

// 可用的测试任务
export const availableTestTasks: TestTask[] = [
  {
    id: 'task_pdq5',
    type: 'PDQ5',
    name: 'PDQ-5量表测试',
    description: '评估记忆力、注意力、思维反应和认知灵活性',
    briefDescription: '基础认知能力自评量表',
    fullDescription: '通过自我评估的方式，测量您在日常生活中的记忆力、注意力、思维反应速度和认知灵活性表现。',
    instructions: ['请根据最近一个月的实际情况回答', '每个问题都有5个选项，请选择最符合您情况的答案', '请诚实作答，这将有助于准确评估您的认知状态'],
    estimatedDuration: 15,
    difficulty: 'easy',
    category: '认知评估',
    icon: '🧠',
    isAvailable: true,
    isMainTask: true,
    order: 1
  },
  {
    id: 'task_hopkins',
    type: 'Hopkins',
    name: 'Hopkins词汇学习测试',
    description: '测试即时记忆和延迟记忆能力',
    briefDescription: '语义记忆和学习能力测试',
    fullDescription: '通过学习和回忆词汇列表，评估您的语义记忆、学习能力和记忆保持能力。包括即时回忆和延迟回忆两个阶段。',
    instructions: ['首先您需要学习一系列词汇', '然后立即进行回忆测试', '经过一段时间后再次进行延迟回忆测试', '请尽您所能地记住学习的内容'],
    estimatedDuration: 20,
    difficulty: 'medium',
    category: '记忆测试',
    icon: '📚',
    isAvailable: true,
    isMainTask: true,
    order: 2
  },
  {
    id: 'task_nback',
    type: 'NBack',
    name: 'N-back工作记忆测试',
    description: '评估工作记忆和注意力持续性',
    estimatedDuration: 25,
    difficulty: 'medium',
    category: '记忆测试',
    icon: '🔢',
    isAvailable: true,
    isMainTask: true,
    order: 3
  },
  {
    id: 'task_stroop',
    type: 'Stroop',
    name: 'Stroop色词测试',
    description: '测试抑制控制能力和认知灵活性',
    briefDescription: '注意力和抑制控制能力测试',
    fullDescription: '测量您在面对冲突信息时的注意力集中能力和抑制不相关反应的能力。您需要快速准确地识别颜色名称的字体颜色。',
    instructions: ['看到颜色名称后，请判断字体的颜色而不是字的意思', '尽可能快速而准确地做出回应', '注意不要被字的含义干扰', '测试分为练习和正式测试两部分'],
    estimatedDuration: 10,
    difficulty: 'hard',
    category: '注意力测试',
    icon: '🎨',
    isAvailable: true,
    isMainTask: true,
    order: 4
  },
  {
    id: 'task_trail',
    type: 'TrailMaking',
    name: '连线测试',
    description: '评估执行功能和视觉-运动协调',
    estimatedDuration: 12,
    difficulty: 'medium',
    category: '执行功能',
    icon: '🔗',
    isAvailable: true,
    isMainTask: true,
    order: 5
  },
  {
    id: 'task_fluency',
    type: 'VerbalFluency',
    name: '词语流畅性测试',
    description: '评估语言功能和语义记忆',
    estimatedDuration: 8,
    difficulty: 'easy',
    category: '语言测试',
    icon: '💬',
    isAvailable: true,
    isMainTask: true,
    order: 6
  },
  {
    id: 'task_cpt',
    type: 'CPT',
    name: '持续性操作测试',
    description: '测试注意力持续性和反应抑制',
    estimatedDuration: 18,
    difficulty: 'hard',
    category: '注意力测试',
    icon: '⏱️',
    isAvailable: true,
    isMainTask: true,
    order: 7
  },
  {
    id: 'task_dsst',
    type: 'DSST',
    name: 'DSST数字符号转换',
    description: '评估处理速度和视觉-运动协调',
    estimatedDuration: 15,
    difficulty: 'medium',
    category: '处理速度',
    icon: '🔢',
    isAvailable: true,
    isMainTask: true,
    order: 8
  },
  {
    id: 'task_masked_emotion',
    type: 'MaskedEmotion',
    name: '掩蔽情感启动任务',
    description: '评估情绪认知和情感处理能力',
    briefDescription: '情绪识别和情感认知测试',
    fullDescription: '通过快速闪现的情绪面孔和掩蔽刺激，评估您的潜意识情绪处理能力和情感认知功能。测试包括练习阶段和正式测试阶段。',
    instructions: [
      '您将看到一个快速闪过的表情之后，紧接着出现一个持续时间较长的表情',
      '请判断持续时间较长的表情传达的情绪是正面的（快乐）还是负面的（愤怒）',
      '使用1-9的数字进行评分：1表示极度负面，9表示极度正面，5表示中性',
      '请根据您的第一感觉快速作答'
    ],
    estimatedDuration: 12,
    difficulty: 'medium',
    category: '情绪认知',
    icon: '😊',
    isAvailable: true,
    isMainTask: true,
    order: 9
  }
]

// 测试套件定义
export const testSuites: TestSuite[] = [
  {
    id: 'main_cognitive_suite',
    name: '认知能力综合测试',
    description: '完整的认知能力评估，包含所有核心测试项目',
    icon: '🧠',
    tasks: [
      'task_pdq5',
      'task_hopkins',
      'task_nback',
      'task_stroop',
      'task_trail',
      'task_fluency',
      'task_cpt',
      'task_dsst',
      'task_masked_emotion'
    ],
    estimatedDuration: 135, // 所有测试总时长（增加了12分钟的情绪掩蔽任务）
    isCompleted: false,
    completionRate: 0,
    suiteNumber: 'CS-2024-001',
    createdDate: '2024-01-15',
    status: 'not_started'
  }
]

// 获取用户可用的测试任务
export const getAvailableTestTasks = (): TestTask[] => {
  return availableTestTasks.filter(task => task.isAvailable)
}

// 获取主要测试任务（按顺序排列）
export const getMainTestTasks = (): TestTask[] => {
  return availableTestTasks
    .filter(task => task.isMainTask && task.isAvailable)
    .sort((a, b) => a.order - b.order)
}

// 获取可重测的任务（主要测试完成后）
export const getRetestAvailableTasks = (completedTaskIds: string[]): TestTask[] => {
  const mainTasks = getMainTestTasks()
  const allMainTasksCompleted = mainTasks.every(task => 
    completedTaskIds.includes(task.id)
  )
  
  if (allMainTasksCompleted) {
    return availableTestTasks.filter(task => task.isAvailable)
  }
  
  // 如果主要测试未完成，只返回主要测试任务
  return mainTasks
}

// 计算测试套件完成率
export const calculateSuiteProgress = (suiteId: string, completedTaskIds: string[]): number => {
  const suite = testSuites.find(s => s.id === suiteId)
  if (!suite) return 0
  
  const completedCount = suite.tasks.filter(taskId => 
    completedTaskIds.includes(taskId)
  ).length
  
  return Math.round((completedCount / suite.tasks.length) * 100)
}

// 检查是否可以进行单项重测
export const canRetestIndividualTasks = (completedTaskIds: string[]): boolean => {
  const mainTasks = getMainTestTasks()
  return mainTasks.every(task => completedTaskIds.includes(task.id))
}